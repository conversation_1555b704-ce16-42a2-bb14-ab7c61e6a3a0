'use server';

/**
 * @fileOverview AI-powered log analyzer for pattern and trend insights.
 *
 * - analyzeLogs - Analyzes logged data for insights.
 * - AnalyzeLogsInput - The input type for the analyzeLogs function.
 * - AnalyzeLogsOutput - The return type for the analyzeLogs function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const AnalyzeLogsInputSchema = z.object({
  logData: z
    .string()
    .describe('The logged data in JSON format to be analyzed.'),
});
export type AnalyzeLogsInput = z.infer<typeof AnalyzeLogsInputSchema>;

const AnalyzeLogsOutputSchema = z.object({
  insights: z.string().describe('Insights on patterns and trends in the log data.'),
  suggestions: z.string().describe('Suggestions for optimizing activities based on the insights.'),
});
export type AnalyzeLogsOutput = z.infer<typeof AnalyzeLogsOutputSchema>;

export async function analyzeLogs(input: AnalyzeLogsInput): Promise<AnalyzeLogsOutput> {
  return analyzeLogsFlow(input);
}

const analyzeLogsPrompt = ai.definePrompt({
  name: 'analyzeLogsPrompt',
  input: {schema: AnalyzeLogsInputSchema},
  output: {schema: AnalyzeLogsOutputSchema},
  prompt: `You are an AI-powered log analyzer. Analyze the following log data for patterns and trends, such as time gaps between tasks or correlations between amounts. Provide insights and suggestions for optimizing activities.

Log Data:
{{logData}}`,
});

const analyzeLogsFlow = ai.defineFlow(
  {
    name: 'analyzeLogsFlow',
    inputSchema: AnalyzeLogsInputSchema,
    outputSchema: AnalyzeLogsOutputSchema,
  },
  async input => {
    const {output} = await analyzeLogsPrompt(input);
    return output!;
  }
);
