
export type LogType = 'number' | 'memo' | 'both' | 'counter';

export interface Log {
  id: string;
  timestamp: Date;
  amount?: number;
  note?: string;
  color?: string;
}

export interface Event {
  id: string;
  name: string;
  icon: string; // lucide-react icon name
  logType: LogType;
  logs: Log[];
  color?: string; // Optional color property for the event
  unit?: string; // e.g., 'kg', 'reps'
  allowDecimals?: boolean;
  defaultAmount?: number;
  defaultNote?: string;
}
