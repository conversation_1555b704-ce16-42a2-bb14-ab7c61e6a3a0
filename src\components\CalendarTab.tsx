
"use client";

import { useState, useMemo, useContext } from "react";
import { format } from "date-fns";
import { type Event, type Log, type LogType } from "@/lib/types";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { MoreVertical, Pencil, Trash2 } from "lucide-react";
import { Button } from "./ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "./ui/dropdown-menu";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "./ui/alert-dialog";
import { EventContext } from "@/context/EventContext";
import { EditLogDialog } from "./EditLogDialog";
import { useToast } from "@/hooks/use-toast";
import { ThemeContext } from "@/context/ThemeContext";

type CalendarTabProps = {
  event: Event;
};

export function CalendarTab({ event }: CalendarTabProps) {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [isEditDialogOpen, setEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedLog, setSelectedLog] = useState<Log | null>(null);

  const eventContext = useContext(EventContext);
  const themeContext = useContext(ThemeContext);
  const { toast } = useToast();

  const logsByDay = useMemo(() => {
    return event.logs.reduce((acc, log) => {
      const day = format(new Date(log.timestamp), "yyyy-MM-dd");
      if (!acc[day]) {
        acc[day] = [];
      }
      acc[day].push(log);
      return acc;
    }, {} as Record<string, Log[]>);
  }, [event.logs]);
  
  const selectedDay = date ? format(date, "yyyy-MM-dd") : "";
  const logsForSelectedDay = logsByDay[selectedDay] || [];

  const dailyStats = useMemo(() => {
    if (logsForSelectedDay.length === 0) {
      return { totalLogs: 0, totalAmount: 0, averageAmount: 0 };
    }
    const numericLogs = logsForSelectedDay.filter(log => typeof log.amount === 'number');
    const totalAmount = numericLogs.reduce((sum, log) => sum + (log.amount || 0), 0);
    const averageAmount = numericLogs.length > 0 ? totalAmount / numericLogs.length : 0;
    return {
      totalLogs: logsForSelectedDay.length,
      totalAmount,
      averageAmount,
    };
  }, [logsForSelectedDay]);

  const loggedDays = Object.keys(logsByDay).map(day => new Date(day));

  const handleEditClick = (log: Log) => {
    setSelectedLog(log);
    setEditDialogOpen(true);
  };

  const handleDeleteClick = (log: Log) => {
    setSelectedLog(log);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (selectedLog) {
      eventContext?.deleteLog(event.id, selectedLog.id);
      toast({
        title: "Log Deleted",
        description: "The log entry has been successfully removed.",
      });
      setDeleteDialogOpen(false);
      setSelectedLog(null);
    }
  };
  
  const eventColor = event.color || themeContext?.primaryColor || 'hsl(var(--primary))';
  
  const calendarModifiersStyles = {
    logged: { 
      fontWeight: 'bold',
      textDecoration: 'underline',
      textDecorationColor: eventColor,
      textDecorationThickness: '2px',
      textUnderlineOffset: '0.2rem',
    },
  };

  return (
    <>
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardContent className="p-0 flex justify-center">
            <Calendar
              mode="single"
              selected={date}
              onSelect={setDate}
              modifiers={{ logged: loggedDays }}
              modifiersStyles={calendarModifiersStyles}
              className="w-full"
            />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>
              Logs for {date ? format(date, "MMMM d, yyyy") : "..."}
            </CardTitle>
            <CardDescription>
              {dailyStats.totalLogs} {dailyStats.totalLogs === 1 ? 'log' : 'logs'} today.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {(event.logType === 'number' || event.logType === 'both') && logsForSelectedDay.length > 0 && (
              <div className="mb-4 grid grid-cols-2 gap-4 text-center">
                <div>
                    <p className="text-sm text-muted-foreground">Total Value</p>
                    <p className="text-2xl font-bold">{dailyStats.totalAmount.toLocaleString()} {event.unit}</p>
                </div>
                <div>
                    <p className="text-sm text-muted-foreground">Average Value</p>
                    <p className="text-2xl font-bold">{dailyStats.averageAmount.toFixed(2)} {event.unit}</p>
                </div>
              </div>
            )}
            <ScrollArea className="h-60">
              {logsForSelectedDay.length > 0 ? (
                <div className="space-y-4">
                  {logsForSelectedDay.slice().sort((a,b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).map(log => (
                    <div key={log.id} className="group relative rounded-lg border-2 p-3 text-sm" style={{ borderColor: log.color || eventColor }}>
                      <div className="flex items-start justify-between">
                         <p className="font-semibold text-muted-foreground">
                          {format(new Date(log.timestamp), "h:mm:ss a")}
                        </p>
                         <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-6 w-6 opacity-0 group-hover:opacity-100">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditClick(log)}>
                              <Pencil className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDeleteClick(log)} className="text-destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      <div className="mt-1 flex flex-col gap-2">
                        {log.amount !== undefined && (
                          <Badge variant="secondary" className="w-fit">
                            Value: {log.amount} {event.unit}
                          </Badge>
                        )}
                        {log.note && <p className="text-sm">{log.note}</p>}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex h-full items-center justify-center text-center text-muted-foreground">
                  <p>No logs for this day.</p>
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {selectedLog && (
        <EditLogDialog
          open={isEditDialogOpen}
          onOpenChange={setEditDialogOpen}
          eventId={event.id}
          log={selectedLog}
          logType={event.logType}
        />
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this log entry.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
