
"use client";

import { useContext } from "react";
import { ThemeContext } from "@/context/ThemeContext";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "./ui/separator";

export function ThemeTab() {
  const themeContext = useContext(ThemeContext);

  if (!themeContext) {
    return null;
  }

  const { accentColor, setAccentColor, primaryColor, setPrimaryColor, backgroundColor, setBackgroundColor } = themeContext;

  return (
    <div className="p-4 space-y-6">
        <h3 className="font-semibold">Customize Theme</h3>
        <Separator />
        <div className="space-y-4">
            <div className="space-y-2">
                <Label htmlFor="primary-color" className="text-sm">Primary Color</Label>
                <div className="flex items-center gap-2">
                    <Input
                        id="primary-color"
                        type="color"
                        value={primaryColor}
                        onChange={(e) => setPrimaryColor(e.target.value)}
                        className="h-8 w-10 p-1"
                    />
                    <span className="text-xs text-muted-foreground">Buttons & active elements</span>
                </div>
            </div>
            <div className="space-y-2">
                <Label htmlFor="accent-color" className="text-sm">Accent Color</Label>
                 <div className="flex items-center gap-2">
                    <Input
                        id="accent-color"
                        type="color"
                        value={accentColor}
                        onChange={(e) => setAccentColor(e.target.value)}
                        className="h-8 w-10 p-1"
                    />
                    <span className="text-xs text-muted-foreground">Highlights & focus rings</span>
                </div>
            </div>
            <div className="space-y-2">
                <Label htmlFor="background-color" className="text-sm">Background Color</Label>
                 <div className="flex items-center gap-2">
                    <Input
                        id="background-color"
                        type="color"
                        value={backgroundColor}
                        onChange={(e) => setBackgroundColor(e.target.value)}
                        className="h-8 w-10 p-1"
                    />
                    <span className="text-xs text-muted-foreground">App background</span>
                </div>
            </div>
        </div>
    </div>
  );
}
