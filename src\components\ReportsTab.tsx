
"use client";

import { useState, useMemo, useContext } from "react";
import {
  Bar,
  <PERSON>hart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { type Event } from "@/lib/types";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "./ui/card";
import { format, differenceInDays, addDays, startOfDay } from "date-fns";
import { ThemeContext } from "@/context/ThemeContext";
import { Button } from "./ui/button";
import { Popover, PopoverTrigger, PopoverContent } from "./ui/popover";
import { Calendar as CalendarIcon, Search, ArrowDownUp } from "lucide-react";
import { Calendar } from "./ui/calendar";
import { Input } from "./ui/input";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "./ui/select";
import { cn } from "@/lib/utils";

type ReportsTabProps = {
  event: Event;
};

export function ReportsTab({ event }: ReportsTabProps) {
  const { logs, logType, unit } = event;
  const themeContext = useContext(ThemeContext);

  const [fromDate, setFromDate] = useState<Date | undefined>(
    logs.length > 0 ? new Date(logs[logs.length - 1].timestamp) : undefined
  );
  const [toDate, setToDate] = useState<Date | undefined>(
    logs.length > 0 ? new Date(logs[0].timestamp) : undefined
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("date-desc");

  const filteredAndSortedLogs = useMemo(() => {
    let filtered = logs;

    // Filter by date range
    if (fromDate) {
      filtered = filtered.filter(log => new Date(log.timestamp) >= startOfDay(fromDate));
    }
    if (toDate) {
      filtered = filtered.filter(log => new Date(log.timestamp) <= addDays(startOfDay(toDate), 1));
    }

    // Filter by search term (notes only)
    if (searchTerm && (logType === 'memo' || logType === 'both')) {
      filtered = filtered.filter(log =>
        log.note?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Sort
    return [...filtered].sort((a, b) => {
        switch (sortBy) {
            case 'date-asc':
                return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
            case 'amount-desc':
                return (b.amount ?? -1) - (a.amount ?? -1);
            case 'amount-asc':
                return (a.amount ?? -1) - (b.amount ?? -1);
            case 'date-desc':
            default:
                return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
        }
    });

  }, [logs, fromDate, toDate, searchTerm, sortBy, logType]);


  const reportStats = useMemo(() => {
    const numericLogs = filteredAndSortedLogs.filter(log => typeof log.amount === 'number');
    const totalLogs = filteredAndSortedLogs.length;
    const totalAmount = numericLogs.reduce((sum, log) => sum + (log.amount || 0), 0);
    const averageAmount = numericLogs.length > 0 ? totalAmount / numericLogs.length : 0;
    
    let averageFrequency = 0;
    if (totalLogs > 1) {
        const sorted = filteredAndSortedLogs.map(l => new Date(l.timestamp).getTime()).sort((a,b) => a-b);
        const firstLogDate = new Date(sorted[0]);
        const lastLogDate = new Date(sorted[sorted.length - 1]);
        const durationDays = differenceInDays(lastLogDate, firstLogDate) + 1;
        averageFrequency = durationDays > 0 ? totalLogs / durationDays : totalLogs;
    } else if (totalLogs === 1) {
        averageFrequency = 1;
    }
    
    return { totalLogs, totalAmount, averageAmount, averageFrequency };
  }, [filteredAndSortedLogs]);

  const chartData = filteredAndSortedLogs
    .map(log => ({
      date: format(new Date(log.timestamp), "MMM d"),
      value: (logType === 'memo' || logType === 'counter') ? 1 : (log.amount || 0),
    }))
    .reverse();

  const color = event.color || themeContext?.primaryColor || 'hsl(var(--primary))';
  
  if (logs.length < 2) {
    return (
      <div className="flex min-h-[40vh] items-center justify-center rounded-lg border-2 border-dashed">
        <div className="text-center">
            <p className="text-lg font-semibold">Not Enough Data for Reports</p>
            <p className="text-muted-foreground">Add at least two logs to generate a report.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        <Card>
            <CardHeader>
                <CardTitle>Filters & Options</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-2">
                    <label className="text-sm font-medium">From</label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !fromDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {fromDate ? format(fromDate, "LLL dd, y") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={fromDate}
                          onSelect={setFromDate}
                          disabled={(date) =>
                            (toDate && date > toDate) || date > new Date()
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                </div>
                 <div className="space-y-2">
                    <label className="text-sm font-medium">To</label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !toDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {toDate ? format(toDate, "LLL dd, y") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={toDate}
                          onSelect={setToDate}
                          disabled={(date) =>
                            (fromDate && date < fromDate) || date > new Date()
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                </div>
                 <div className="space-y-2">
                    <label htmlFor="search" className="text-sm font-medium">Search Notes</label>
                    <div className="relative">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                            id="search"
                            type="search"
                            placeholder="Search in notes..."
                            className="pl-9"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            disabled={logType !== 'memo' && logType !== 'both'}
                        />
                    </div>
                 </div>
                 <div className="space-y-2">
                    <label htmlFor="sort" className="text-sm font-medium">Sort By</label>
                     <Select value={sortBy} onValueChange={setSortBy}>
                        <SelectTrigger id="sort" className="w-full">
                           <ArrowDownUp className="mr-2 h-4 w-4" />
                           <SelectValue placeholder="Sort by..." />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="date-desc">Date (Newest first)</SelectItem>
                            <SelectItem value="date-asc">Date (Oldest first)</SelectItem>
                            {(logType === 'number' || logType === 'both') && (
                                <>
                                <SelectItem value="amount-desc">Value (High to Low)</SelectItem>
                                <SelectItem value="amount-asc">Value (Low to High)</SelectItem>
                                </>
                            )}
                        </SelectContent>
                     </Select>
                 </div>
            </CardContent>
        </Card>

        {filteredAndSortedLogs.length === 0 ? (
            <div className="flex min-h-[40vh] items-center justify-center rounded-lg border-2 border-dashed">
                <div className="text-center">
                    <p className="text-lg font-semibold">No Matching Logs Found</p>
                    <p className="text-muted-foreground">Try adjusting your filters.</p>
                </div>
            </div>
        ) : (
            <>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader>
                    <CardTitle>Total Logs</CardTitle>
                    <CardDescription>Total entries in selection</CardDescription>
                    </CardHeader>
                    <CardContent>
                    <p className="text-2xl md:text-4xl font-bold">{reportStats.totalLogs}</p>
                    </CardContent>
                </Card>
                { (logType === 'number' || logType === 'both') && (
                    <>
                    <Card>
                        <CardHeader>
                        <CardTitle>Total Amount</CardTitle>
                        <CardDescription>Sum of all selected entries</CardDescription>
                        </CardHeader>
                        <CardContent>
                        <p className="text-2xl md:text-4xl font-bold">{reportStats.totalAmount.toLocaleString()} {unit}</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader>
                        <CardTitle>Average Amount</CardTitle>
                        <CardDescription>Mean value of selected entries</CardDescription>
                        </CardHeader>
                        <CardContent>
                        <p className="text-2xl md:text-4xl font-bold">{reportStats.averageAmount.toFixed(2)} {unit}</p>
                        </CardContent>
                    </Card>
                    </>
                )}
                <Card>
                    <CardHeader>
                    <CardTitle>Avg. Frequency</CardTitle>
                    <CardDescription>Average logs per day</CardDescription>
                    </CardHeader>
                    <CardContent>
                    <p className="text-2xl md:text-4xl font-bold">{reportStats.averageFrequency.toFixed(2)}</p>
                    </CardContent>
                </Card>
                </div>

                <Card className="md:col-span-2 lg:col-span-4">
                    <CardHeader>
                    <CardTitle>Log Activity Over Time</CardTitle>
                    <CardDescription>
                        A visual representation of your filtered log entries.
                    </CardDescription>
                    </CardHeader>
                    <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" vertical={false} />
                        <XAxis dataKey="date" tick={{ fill: "hsl(var(--muted-foreground))" }} fontSize={12} />
                        <YAxis tick={{ fill: "hsl(var(--muted-foreground))" }} fontSize={12} />
                        <Tooltip
                            contentStyle={{
                            background: "hsl(var(--background))",
                            borderColor: "hsl(var(--border))",
                            }}
                            formatter={(value: number) => [`${value}${unit ? ` ${unit}` : ''}`, 'Value']}
                        />
                        <Bar dataKey="value" fill={color} radius={[4, 4, 0, 0]} />
                        </BarChart>
                    </ResponsiveContainer>
                    </CardContent>
                </Card>
            </>
        )}
    </div>
  );
}
