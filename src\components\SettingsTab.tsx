
"use client";

import { useContext, useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Save, Trash2, Al<PERSON><PERSON>riangle } from "lucide-react";

import { type Event } from "@/lib/types";
import { EventContext } from "@/context/EventContext";
import { useToast } from "@/hooks/use-toast";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/alert-dialog";
import { Label } from "./ui/label";
import { Switch } from "./ui/switch";

type SettingsTabProps = {
  event: Event;
};

const formSchema = z.object({
  name: z.string().min(2, "Event name must be at least 2 characters.").max(50),
  icon: z.string().min(1, "Icon name is required.").max(50),
  color: z.string().optional(),
  unit: z.string().max(10, "Unit should be 10 characters or less.").optional(),
  allowDecimals: z.boolean().optional(),
  defaultAmount: z.coerce.number().optional(),
  defaultNote: z.string().max(500, "Default note is too long.").optional(),
});

export function SettingsTab({ event }: SettingsTabProps) {
  const router = useRouter();
  const context = useContext(EventContext);
  const { toast } = useToast();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: event.name,
      icon: event.icon,
      color: event.color || '#808B96',
      unit: event.unit || '',
      allowDecimals: event.allowDecimals !== false,
      defaultAmount: event.defaultAmount ?? undefined,
      defaultNote: event.defaultNote || '',
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    context?.updateEvent(event.id, values);
    toast({
      title: "Settings Saved",
      description: "Your event details have been updated.",
    });
  }

  function onDelete() {
    context?.deleteEvent(event.id);
    toast({
      title: "Event Deleted",
      description: `The "${event.name}" event has been removed.`,
      variant: "destructive",
    });
    router.push("/");
  }

  return (
    <div className="grid gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>General Settings</CardTitle>
          <CardDescription>Update your event's name, icon, and color.</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Event Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Feed Fish" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="icon"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Icon Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Fish" {...field} />
                    </FormControl>
                    <FormDescription>
                      Provide a valid icon name from{" "}
                      <a href="https://lucide.dev/icons/" target="_blank" rel="noopener noreferrer" className="underline">lucide.dev</a>.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="color"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <Label>Event Color</Label>
                    <div className="flex items-center gap-2">
                       <FormControl>
                          <Input type="color" {...field} className="h-10 w-14 p-1" />
                       </FormControl>
                       <span className="text-sm text-muted-foreground">Click to pick a color for this event.</span>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <CardTitle className="text-xl border-t pt-6">Log Settings</CardTitle>
              
              {(event.logType === "number" || event.logType === "both") && (
                 <>
                  <FormField
                    control={form.control}
                    name="unit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Unit</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., kg, reps, pages" {...field} />
                        </FormControl>
                        <FormDescription>A custom unit to display next to numeric logs.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                   <FormField
                    control={form.control}
                    name="allowDecimals"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Allow Decimals</FormLabel>
                          <FormDescription>
                            Allow decimal values for numeric logs.
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="defaultAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Amount</FormLabel>
                        <FormControl>
                          <Input type="number" placeholder="e.g., 1" {...field} 
                          value={field.value ?? ''}
                          onChange={e => field.onChange(e.target.value === '' ? undefined : +e.target.value)}
                          step={form.getValues('allowDecimals') ? "any" : "1"} />
                        </FormControl>
                         <FormDescription>A default value to pre-fill when adding a new log.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                 </>
              )}

              {(event.logType === "memo" || event.logType === "both") && (
                 <FormField
                    control={form.control}
                    name="defaultNote"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Note</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Morning session" {...field} />
                        </FormControl>
                         <FormDescription>A default note to pre-fill when adding a new log.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
              )}

              <Button type="submit">
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
      
      <Card className="border-destructive h-fit">
        <CardHeader>
          <CardTitle>Danger Zone</CardTitle>
          <CardDescription>
            These actions are permanent and cannot be undone.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Event
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className="flex items-center gap-2">
                    <AlertTriangle className="text-destructive"/> Are you absolutely sure?
                </AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete the
                  "{event.name}" event and all of its associated log data.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={onDelete} className="bg-destructive hover:bg-destructive/90">
                  Yes, delete it
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </CardContent>
      </Card>
    </div>
  );
}
