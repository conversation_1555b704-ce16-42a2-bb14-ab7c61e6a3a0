
"use client";

import { useContext, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Save, Calendar as CalendarIcon } from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { EventContext } from "@/context/EventContext";
import { type Log, type LogType } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { Label } from "./ui/label";

type EditLogDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  eventId: string;
  log: Log;
  logType: LogType;
};

export function EditLogDialog({ open, onOpenChange, eventId, log, logType }: EditLogDialogProps) {
  const context = useContext(EventContext);
  const { toast } = useToast();

  const generateSchema = () => {
    let schema = z.object({
        date: z.date(),
        time: z.string(),
        color: z.string().optional(),
    });
    if (logType === "number" || logType === "both") {
      schema = schema.extend({
        amount: z.coerce.number().min(0, "Amount must be a positive number.").optional(),
      });
    }
    if (logType === "memo" || logType === "both") {
      schema = schema.extend({
        note: z.string().max(500, "Note is too long.").optional(),
      });
    }
    return schema;
  };

  const formSchema = generateSchema();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      date: new Date(log.timestamp),
      time: format(new Date(log.timestamp), "HH:mm:ss"),
      amount: log.amount,
      note: log.note,
      color: log.color || '',
    },
  });
  
  useEffect(() => {
    form.reset({
      date: new Date(log.timestamp),
      time: format(new Date(log.timestamp), "HH:mm:ss"),
      amount: log.amount,
      note: log.note,
      color: log.color || '',
    });
  }, [log, form]);

  function onSubmit(values: z.infer<typeof formSchema>) {
    let timestamp = new Date(values.date);
    const [hours, minutes, seconds] = values.time.split(':').map(Number);
    timestamp.setHours(hours, minutes, seconds || 0);

    const updatedLogData = {
      timestamp,
      amount: values.amount,
      note: values.note,
      color: values.color,
    };
    
    context?.updateLog(eventId, log.id, updatedLogData);
    toast({
      title: "Log Updated",
      description: "Your entry has been successfully updated.",
    });
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Log Entry</DialogTitle>
          <DialogDescription>Modify the details of this log entry.</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? format(field.value, "PPP") : <span>Pick a date</span>}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="time"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Time</FormLabel>
                    <FormControl>
                      <Input type="time" step="1" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {(logType === "number" || logType === "both") && (
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount / Value</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="e.g., 2.5" {...field} value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            {(logType === "memo" || logType === "both") && (
              <FormField
                control={form.control}
                name="note"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Note</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any relevant notes..."
                        className="resize-none"
                        {...field}
                         value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
             <FormField
                control={form.control}
                name="color"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <Label>Log Color (Optional)</Label>
                    <div className="flex items-center gap-2">
                       <FormControl>
                          <Input type="color" {...field} className="h-10 w-14 p-1" value={field.value || ''} />
                       </FormControl>
                       <span className="text-sm text-muted-foreground">Pick a color for this specific log.</span>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            <Button type="submit" className="w-full">
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
