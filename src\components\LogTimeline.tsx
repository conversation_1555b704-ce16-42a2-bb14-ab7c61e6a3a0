
"use client";

import { useState, useContext } from "react";
import { Clock, FileText, Hash, List, MoreVertical, Pencil, Trash2 } from "lucide-react";
import { type Log } from "@/lib/types";
import { formatTimestamp } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "./ui/scroll-area";
import { Button } from "./ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "./ui/alert-dialog";
import { EventContext } from "@/context/EventContext";
import { EditLogDialog } from "./EditLogDialog";
import { useToast } from "@/hooks/use-toast";

type LogTimelineProps = {
  logs: Log[];
  eventId: string;
  logType: "number" | "memo" | "both" | "counter";
  eventColor?: string;
  unit?: string;
};

export function LogTimeline({ logs, eventId, logType, eventColor, unit }: LogTimelineProps) {
  const [isEditDialogOpen, setEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedLog, setSelectedLog] = useState<Log | null>(null);
  const eventContext = useContext(EventContext);
  const { toast } = useToast();

  if (logs.length === 0) {
    return (
      <Card className="flex h-full min-h-[400px] items-center justify-center lg:min-h-[60vh]">
        <div className="text-center text-muted-foreground">
          <List className="mx-auto h-12 w-12" />
          <h3 className="mt-4 text-lg font-semibold">No Logs Yet</h3>
          <p className="mt-2 text-sm">Add a new log to see your history here.</p>
        </div>
      </Card>
    );
  }

  const handleEditClick = (log: Log) => {
    setSelectedLog(log);
    setEditDialogOpen(true);
  };

  const handleDeleteClick = (log: Log) => {
    setSelectedLog(log);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (selectedLog) {
      eventContext?.deleteLog(eventId, selectedLog.id);
      toast({
        title: "Log Deleted",
        description: "The log entry has been successfully removed.",
      });
      setDeleteDialogOpen(false);
      setSelectedLog(null);
    }
  };

  const color = eventColor || 'hsl(var(--primary))';

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Log History</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[60vh] pr-4">
            <div className="relative space-y-8 pl-8 before:absolute before:inset-0 before:ml-3 before:w-px before:bg-border">
              {logs.map((log) => {
                const logColor = log.color || color;
                const accentStyle = { color: logColor };
                const timelineDotStyle = { backgroundColor: logColor };
                const logCardStyle = { borderColor: logColor };

                return (
                  <div key={log.id} className="relative group">
                    <div className="absolute left-0 top-1.5 z-10 -translate-x-1/2">
                      <div className="grid h-6 w-6 place-items-center rounded-full text-primary-foreground" style={timelineDotStyle}>
                          <Clock className="h-3 w-3" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-muted-foreground">
                          {formatTimestamp(new Date(log.timestamp))}
                        </p>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-7 w-7 opacity-0 group-hover:opacity-100">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditClick(log)}>
                              <Pencil className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDeleteClick(log)} className="text-destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                       <div className="rounded-lg border-2 bg-card p-4 shadow-sm space-y-2" style={logCardStyle}>
                        {log.amount === undefined && (log.note === undefined || log.note === "") && logType === 'counter' && (
                          <div className="flex items-center gap-3">
                            <Clock className="h-4 w-4 flex-shrink-0" style={accentStyle} />
                            <p className="text-sm text-foreground">Logged</p>
                          </div>
                        )}
                        {log.amount !== undefined && (
                          <div className="flex items-start gap-3">
                            <Hash className="h-4 w-4 mt-0.5 flex-shrink-0" style={accentStyle} />
                            <p className="text-sm font-semibold">{log.amount}{unit && ` ${unit}`}</p>
                          </div>
                        )}
                        {log.note && (
                          <div className="flex items-start gap-3">
                            <FileText className="h-4 w-4 mt-0.5 flex-shrink-0" style={accentStyle} />
                            <p className="text-sm text-foreground">{log.note}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
      
      {selectedLog && (
        <EditLogDialog
          open={isEditDialogOpen}
          onOpenChange={setEditDialogOpen}
          eventId={eventId}
          log={selectedLog}
          logType={logType}
        />
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this log entry.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
