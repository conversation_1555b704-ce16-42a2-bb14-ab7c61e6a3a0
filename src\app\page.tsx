
"use client";

import { useState, useContext, useRef } from "react";
import { Plus, Moon, Sun, Palette, HardDriveDownload, HardDriveUpload } from "lucide-react";

import { Button } from "@/components/ui/button";
import { CreateEventDialog } from "@/components/CreateEventDialog";
import { EventContext } from "@/context/EventContext";
import { ThemeContext } from "@/context/ThemeContext";
import { EventCard } from "@/components/EventCard";
import { ChronoLogIcon } from "@/components/icons";
import { Skeleton } from "@/components/ui/skeleton";
import { ThemeTab } from "@/components/ThemeTab";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { ImportDataDialog } from "@/components/ImportDataDialog";
import { type Event } from "@/lib/types";


export default function Home() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const eventContext = useContext(EventContext);
  const themeContext = useContext(ThemeContext);
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  if (!eventContext || !themeContext) {
    // This should not happen as page is wrapped in provider
    return <div>Error: Context not found</div>;
  }

  const { events, loading, importEvents } = eventContext;
  const { theme, toggleTheme } = themeContext;
  
  const handleExport = () => {
    try {
        const dataStr = JSON.stringify(events, null, 2);
        const dataBlob = new Blob([dataStr], { type: "application/json" });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "chronolog-data.json";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        toast({
            title: "Export Successful",
            description: "Your data has been downloaded.",
        });
    } catch (error) {
        toast({
            title: "Export Failed",
            description: "Could not export your data.",
            variant: "destructive",
        });
    }
  };

  const handleImportClick = () => {
    setIsImportDialogOpen(true);
  };
  
  const confirmImport = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
        try {
            const result = event.target?.result;
            if (typeof result === "string") {
                const importedData = JSON.parse(result) as Event[];
                // Add some basic validation
                if (Array.isArray(importedData) && importedData.every(item => item.id && item.name)) {
                     if(importEvents) {
                       importEvents(importedData);
                       toast({
                           title: "Import Successful",
                           description: "Your data has been restored.",
                       });
                     }
                } else {
                    throw new Error("Invalid file format.");
                }
            }
        } catch (error) {
            toast({
                title: "Import Failed",
                description: "The selected file is not valid.",
                variant: "destructive",
            });
        }
    };
    reader.readAsText(file);
     if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };


  return (
    <>
      <div className="min-h-screen bg-background">
        <header className="sticky top-0 z-10 border-b bg-background/80 backdrop-blur-sm">
          <div className="container mx-auto flex h-16 items-center justify-between px-4 md:px-6">
            <div className="flex items-center gap-3">
              <ChronoLogIcon className="h-8 w-8 text-primary" />
              <h1 className="text-2xl font-bold text-foreground">ChronoLog</h1>
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={() => setIsDialogOpen(true)} size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Event
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Palette className="h-5 w-5" />
                    <span className="sr-only">Customize theme</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <ThemeTab />
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                 <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <HardDriveDownload className="h-5 w-5" />
                    <span className="sr-only">Data management</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleImportClick}>
                      <HardDriveUpload className="mr-2 h-4 w-4" />
                      Import Data
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleExport}>
                      <HardDriveDownload className="mr-2 h-4 w-4" />
                      Export Data
                    </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button variant="ghost" size="icon" onClick={toggleTheme}>
                {theme === 'dark' ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
                <span className="sr-only">Toggle theme</span>
              </Button>
            </div>
          </div>
        </header>

        <main className="container mx-auto p-4 md:p-6">
          {loading ? (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {[...Array(4)].map((_, i) => (
                <Skeleton key={i} className="h-40 w-full rounded-lg" />
              ))}
            </div>
          ) : events.length > 0 ? (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {events.map((event) => (
                <EventCard key={event.id} event={event} />
              ))}
            </div>
          ) : (
            <div className="flex min-h-[60vh] flex-col items-center justify-center rounded-lg border-2 border-dashed border-border bg-card p-12 text-center">
              <div className="mb-4 text-6xl">⏱️</div>
              <h2 className="text-2xl font-semibold tracking-tight text-foreground">
                No events yet
              </h2>
              <p className="mt-2 text-muted-foreground">
                Get started by creating a new event to track.
              </p>
              <Button className="mt-6" onClick={() => setIsDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Event
              </Button>
            </div>
          )}
        </main>
      </div>
      <CreateEventDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} />
       <ImportDataDialog
        open={isImportDialogOpen}
        onOpenChange={setIsImportDialogOpen}
        onConfirm={confirmImport}
      />
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="application/json"
        onChange={handleFileChange}
      />
    </>
  );
}
