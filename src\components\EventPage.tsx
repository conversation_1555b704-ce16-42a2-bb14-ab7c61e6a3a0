"use client";

import { useContext, useEffect, useState } from "react";
import Link from "next/link";
import { ArrowLeft, BarChart3, Bot, Calendar, List, Plus, Settings, Palette } from "lucide-react";
import * as LucideIcons from "lucide-react";

import { EventContext } from "@/context/EventContext";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LogTimeline } from "./LogTimeline";
import { ReportsTab } from "./ReportsTab";
import { SummaryTab } from "./SummaryTab";
import { CalendarTab } from "./CalendarTab";
import { SettingsTab } from "./SettingsTab";
import { AddLogDialog } from "./AddLogDialog";
import { ThemeTab } from "./ThemeTab";
import { ThemeContext } from "@/context/ThemeContext";

type EventPageProps = {
  id: string;
};

// A type guard for Lucide icons
const isIcon = (name: string): name is keyof typeof LucideIcons => {
  return name in LucideIcons;
}

export function EventPage({ id }: EventPageProps) {
  const eventContext = useContext(EventContext);
  const themeContext = useContext(ThemeContext);
  const [isClient, setIsClient] = useState(false);
  const [isAddLogDialogOpen, setAddLogDialogOpen] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!eventContext || !themeContext) {
    return <div>Loading...</div>;
  }
  
  const { getEventById, loading } = eventContext;
  const event = getEventById(id);

  if (loading || !isClient) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-xl">Loading Event...</div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="flex h-screen flex-col items-center justify-center text-center">
        <h1 className="text-4xl font-bold">Event Not Found</h1>
        <p className="mt-2 text-muted-foreground">The event you are looking for does not exist.</p>
        <Button asChild className="mt-6">
          <Link href="/">
            <ArrowLeft className="mr-2 h-4 w-4" /> Go Back to Dashboard
          </Link>
        </Button>
      </div>
    );
  }

  const Icon = isIcon(event.icon) ? LucideIcons[event.icon] as React.ElementType : LucideIcons.Activity;

  const eventColor = event.color || themeContext.primaryColor;

  return (
    <>
      <div className="min-h-screen bg-background">
        <header className="sticky top-0 z-10 border-b bg-background/80 backdrop-blur-sm">
          <div className="container mx-auto flex h-16 items-center justify-between px-4 md:px-6">
            <div className="flex items-center gap-4">
              <Button asChild variant="ghost" size="icon">
                <Link href="/">
                  <ArrowLeft className="h-5 w-5" />
                </Link>
              </Button>
              <div className="flex items-center gap-3">
                <Icon className="h-7 w-7" style={{ color: eventColor }} />
                <h1 className="text-2xl font-bold text-foreground">{event.name}</h1>
              </div>
            </div>
             <Button onClick={() => setAddLogDialogOpen(true)} size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Log
            </Button>
          </div>
        </header>

        <main className="container mx-auto p-4 md:p-6">
          <Tabs defaultValue="logs" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="logs"><List className="mr-2 h-4 w-4" />Logs</TabsTrigger>
              <TabsTrigger value="calendar"><Calendar className="mr-2 h-4 w-4" />Calendar</TabsTrigger>
              <TabsTrigger value="reports"><BarChart3 className="mr-2 h-4 w-4" />Reports</TabsTrigger>
              <TabsTrigger value="summary"><Bot className="mr-2 h-4 w-4" />Summary</TabsTrigger>
              <TabsTrigger value="theme"><Palette className="mr-2 h-4 w-4" />Theme</TabsTrigger>
              <TabsTrigger value="settings"><Settings className="mr-2 h-4 w-4" />Settings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="logs" className="mt-6">
                <LogTimeline logs={event.logs} eventId={event.id} logType={event.logType} eventColor={eventColor} />
            </TabsContent>

            <TabsContent value="calendar" className="mt-6">
              <CalendarTab event={event} />
            </TabsContent>

            <TabsContent value="reports" className="mt-6">
              <ReportsTab event={event} />
            </TabsContent>

            <TabsContent value="summary" className="mt-6">
              <SummaryTab event={event} />
            </TabsContent>

            <TabsContent value="theme" className="mt-6">
              <ThemeTab />
            </TabsContent>

            <TabsContent value="settings" className="mt-6">
              <SettingsTab event={event} />
            </TabsContent>
          </Tabs>
        </main>
      </div>
      <AddLogDialog
        open={isAddLogDialogOpen}
        onOpenChange={setAddLogDialogOpen}
        eventId={event.id}
        logType={event.logType}
      />
    </>
  );
}
