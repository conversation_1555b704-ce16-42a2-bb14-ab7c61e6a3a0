
"use client";

import { useState, useContext, useEffect } from "react";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import * as LucideIcons from "lucide-react";
import { Plus } from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { EventContext } from "@/context/EventContext";
import { useToast } from "@/hooks/use-toast";
import { type Event } from "@/lib/types";
import { ThemeContext } from "@/context/ThemeContext";

type EventCardProps = {
  event: Event;
};

// A type guard for Lucide icons
const isIcon = (name: string): name is keyof typeof LucideIcons => {
  return name in LucideIcons;
}

export function EventCard({ event }: EventCardProps) {
  const context = useContext(EventContext);
  const themeContext = useContext(ThemeContext);
  const { toast } = useToast();
  const [amount, setAmount] = useState<number | undefined>(event.defaultAmount);
  const [note, setNote] = useState<string | undefined>(event.defaultNote);
  
  useEffect(() => {
    setAmount(event.defaultAmount);
    setNote(event.defaultNote);
  }, [event.defaultAmount, event.defaultNote]);
  
  const Icon = isIcon(event.icon) ? LucideIcons[event.icon] as React.ElementType : LucideIcons.Activity;

  const lastLogTime = event.logs.length > 0
    ? formatDistanceToNow(new Date(event.logs[0].timestamp), { addSuffix: true })
    : "No logs yet";

  const handleAddLog = () => {
    if (context) {
        if ((event.logType === 'number' || event.logType === 'both') && (amount === undefined || amount === null || isNaN(amount))) {
             if (event.logType !== 'both' || amount !== undefined) { // only require amount if it is not optional in 'both' type
                toast({
                title: "Invalid Input",
                description: "Please enter a valid number.",
                variant: "destructive",
                });
                return;
            }
        }
        if ((event.logType === 'memo' || event.logType === 'both') && !note) {
          // Allow empty notes
        }
        context.addLog(event.id, { amount, note });
        toast({
            title: "Log Added!",
            description: `New log for "${event.name}" has been recorded.`,
        });
        setAmount(event.defaultAmount);
        setNote(event.defaultNote || "");
    }
  };

  const handleCardClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    // Don't navigate if click is on input/button/textarea
    if (e.target instanceof HTMLInputElement || e.target instanceof HTMLButtonElement || e.target instanceof HTMLTextAreaElement || e.target instanceof SVGSVGElement || e.target instanceof HTMLParagraphElement) {
        e.preventDefault();
    }
  };
  
  const eventColor = event.color || themeContext?.primaryColor || 'hsl(var(--primary))';

  return (
    <>
      <Card
        className="flex h-full transform flex-col border-2 transition-all duration-300 hover:scale-[1.02] hover:shadow-xl"
        style={{ borderColor: event.color ? event.color : 'hsl(var(--border))' }}
        onClick={handleCardClick}
      >
        <Link href={`/events/${event.id}`} className="flex flex-grow flex-col">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-4">
                <Icon className="h-8 w-8" style={{ color: eventColor }} />
                <div className="flex-1">
                  <CardTitle className="font-headline">{event.name}</CardTitle>
                  <CardDescription>
                    {event.logs.length} {event.logs.length === 1 ? 'log' : 'logs'}
                  </CardDescription>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="flex-grow">
            {/* Quick add form elements will be here, but we will place them in the footer for better layout */}
          </CardContent>
          <CardFooter className="flex-col items-start">
             <p className="text-sm text-muted-foreground mb-4">Last log: {lastLogTime}</p>
          </CardFooter>
        </Link>
         <div className="p-4 pt-0">
            <div className="flex w-full items-center space-x-2">
                {event.logType === 'number' && (
                    <Input
                        type="number"
                        placeholder={`Value ${event.unit ? `(${event.unit})` : ''}`}
                        value={amount ?? ''}
                        onChange={(e) => setAmount(parseFloat(e.target.value))}
                        onClick={(e) => e.stopPropagation()}
                        step={event.allowDecimals === false ? "1" : "any"}
                    />
                )}
                {event.logType === 'memo' && (
                    <Input
                        type="text"
                        placeholder="Note"
                        value={note || ''}
                        onChange={(e) => setNote(e.target.value)}
                        onClick={(e) => e.stopPropagation()}
                    />
                )}
                {event.logType === 'both' && (
                    <>
                        <Input
                            type="number"
                            placeholder={`Value ${event.unit ? `(${event.unit})` : ''}`}
                            className="w-1/3"
                            value={amount ?? ''}
                            onChange={(e) => setAmount(parseFloat(e.target.value))}
                            onClick={(e) => e.stopPropagation()}
                            step={event.allowDecimals === false ? "1" : "any"}
                        />
                        <Input
                            type="text"
                            placeholder="Note"
                            className="flex-1"
                            value={note || ''}
                            onChange={(e) => setNote(e.target.value)}
                            onClick={(e) => e.stopPropagation()}
                        />
                    </>
                )}
                <Button variant="outline" size="icon" onClick={handleAddLog}>
                    <Plus className="h-4 w-4" />
                </Button>
            </div>
        </div>
      </Card>
    </>
  );
}
