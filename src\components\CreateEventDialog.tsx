"use client";

import { useContext } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Activity, Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { EventContext } from "@/context/EventContext";
import { useToast } from "@/hooks/use-toast";
import { Label } from "./ui/label";

const formSchema = z.object({
  name: z.string().min(2, "Event name must be at least 2 characters.").max(50),
  icon: z.string().min(1, "Icon name is required.").max(50),
  logType: z.enum(["number", "memo", "both", "counter"]),
  color: z.string().optional(),
});

type CreateEventDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function CreateEventDialog({ open, onOpenChange }: CreateEventDialogProps) {
  const context = useContext(EventContext);
  const { toast } = useToast();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      icon: "Activity",
      logType: "number",
      color: "#808B96"
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    context?.addEvent(values.name, values.icon, values.logType as "number" | "memo" | "both" | "counter", values.color);
    toast({
      title: "Event Created",
      description: `The "${values.name}" event has been successfully created.`,
    });
    form.reset();
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Event</DialogTitle>
          <DialogDescription>
            Set up a new event to start logging. You can customize how you track it.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Event Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Feed Fish" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="icon"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Icon Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Fish" {...field} />
                    </FormControl>
                    <FormDescription className="text-xs">
                      <a href="https://lucide.dev/icons/" target="_blank" rel="noopener noreferrer" className="underline">lucide.dev</a> icon name.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="color"
                render={({ field }) => (
                  <FormItem>
                    <Label>Event Color</Label>
                    <FormControl>
                      <Input type="color" {...field} className="h-10 w-full p-1" />
                    </FormControl>
                    <FormDescription>Event color.</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="logType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Log Data Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a type for your logs" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="counter">Counter (timestamp only)</SelectItem>
                      <SelectItem value="number">Number (e.g., amount, quantity)</SelectItem>
                      <SelectItem value="memo">Memo (e.g., notes, description)</SelectItem>
                      <SelectItem value="both">Both (Number and Memo)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>This determines what data you'll enter for each log.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="submit">
                <Plus className="mr-2 h-4 w-4" />
                Create Event
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
