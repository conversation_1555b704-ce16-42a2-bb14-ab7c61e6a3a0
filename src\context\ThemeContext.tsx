
"use client";

import React, { createContext, useState, useEffect, ReactNode } from 'react';

type Theme = "light" | "dark";

// Function to convert hex to HSL string
const hexToHslString = (hex: string): string => {
  let r = 0, g = 0, b = 0;
  if (hex.length === 4) {
    r = parseInt(hex[1] + hex[1], 16);
    g = parseInt(hex[2] + hex[2], 16);
    b = parseInt(hex[3] + hex[3], 16);
  } else if (hex.length === 7) {
    r = parseInt(hex.substring(1, 3), 16);
    g = parseInt(hex.substring(3, 5), 16);
    b = parseInt(hex.substring(5, 7), 16);
  }
  
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0, s = 0, l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  h = Math.round(h * 360);
  s = Math.round(s * 100);
  l = Math.round(l * 100);

  return `${h} ${s}% ${l}%`;
};

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  accentColor: string;
  setAccentColor: (color: string) => void;
  primaryColor: string;
  setPrimaryColor: (color: string) => void;
  backgroundColor: string;
  setBackgroundColor: (color: string) => void;
}

export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  const [theme, setTheme] = useState<Theme>('dark');
  const [accentColor, setAccentColorState] = useState('#f97316'); // Default: orange
  const [primaryColor, setPrimaryColorState] = useState('#808B96'); // Default: gray
  const [backgroundColor, setBackgroundColorState] = useState(theme === 'dark' ? '#171D23' : '#FDFAF7');
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    try {
      const storedTheme = localStorage.getItem('chronoLogTheme') as Theme | null;
      const storedAccent = localStorage.getItem('chronoLogAccentColor');
      const storedPrimary = localStorage.getItem('chronoLogPrimaryColor');
      const storedBackground = localStorage.getItem('chronoLogBackgroundColor');
      
      if (storedTheme) setTheme(storedTheme);
      if (storedAccent) setAccentColorState(storedAccent);
      if (storedPrimary) setPrimaryColorState(storedPrimary);
      if (storedBackground) setBackgroundColorState(storedBackground);

    } catch (error) {
      console.error("Failed to load theme from localStorage", error);
    }
  }, []);

  useEffect(() => {
    if (!isMounted) return;
    
    document.documentElement.classList.remove('light', 'dark');
    document.documentElement.classList.add(theme);
    setBackgroundColorState(theme === 'dark' ? '#171D23' : '#FDFAF7');

    try {
        localStorage.setItem('chronoLogTheme', theme);
    } catch (error) {
        console.error("Failed to save theme to localStorage", error);
    }
  }, [theme, isMounted]);

  const setAccentColor = (color: string) => {
    setAccentColorState(color);
    if (isMounted) {
      document.documentElement.style.setProperty('--accent', hexToHslString(color));
      try {
        localStorage.setItem('chronoLogAccentColor', color);
      } catch (error) {
        console.error("Failed to save accent color to localStorage", error);
      }
    }
  };

  const setPrimaryColor = (color: string) => {
    setPrimaryColorState(color);
    if (isMounted) {
      document.documentElement.style.setProperty('--primary', hexToHslString(color));
       try {
        localStorage.setItem('chronoLogPrimaryColor', color);
      } catch (error) {
        console.error("Failed to save primary color to localStorage", error);
      }
    }
  };

  const setBackgroundColor = (color: string) => {
    setBackgroundColorState(color);
    if (isMounted) {
      document.documentElement.style.setProperty('--background', hexToHslString(color));
       try {
        localStorage.setItem('chronoLogBackgroundColor', color);
      } catch (error) {
        console.error("Failed to save background color to localStorage", error);
      }
    }
  }
  
  useEffect(() => {
      if(isMounted) {
        setPrimaryColor(primaryColor);
        setAccentColor(accentColor);
        setBackgroundColor(backgroundColor);
      }
  }, [isMounted, primaryColor, accentColor, backgroundColor]);


  const toggleTheme = () => {
    setTheme(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  const value = { 
      theme, 
      setTheme, 
      toggleTheme, 
      accentColor, 
      setAccentColor,
      primaryColor,
      setPrimaryColor,
      backgroundColor,
      setBackgroundColor
    };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
