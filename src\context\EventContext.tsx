
"use client";

import React, { createContext, useState, useEffect, ReactNode } from 'react';
import type { Event, Log, LogType } from '@/lib/types';

interface EventContextType {
  events: Event[];
  loading: boolean;
  addEvent: (name: string, icon: string, logType: LogType, color?: string) => void;
  addLog: (eventId: string, logData: Partial<Omit<Log, 'id' | 'timestamp'>>, timestamp?: Date) => void;
  getEventById: (id: string) => Event | undefined;
  updateEvent: (eventId: string, updatedData: Partial<Event>) => void;
  deleteEvent: (eventId: string) => void;
  updateLog: (eventId: string, logId: string, updatedLogData: Partial<Omit<Log, 'id'>>) => void;
  deleteLog: (eventId: string, logId: string) => void;
  importEvents: (data: Event[]) => void;
}

export const EventContext = createContext<EventContextType | undefined>(undefined);

const mockEvents: Event[] = [
  {
    id: '1',
    name: 'Feed Fish',
    icon: 'Fish',
    logType: 'number',
    color: '#3b82f6',
    logs: [
      { id: '1-1', timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), amount: 2 },
      { id: '1-2', timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), amount: 2 },
      { id: '1-3', timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), amount: 2.5 },
    ],
  },
  {
    id: '2',
    name: 'Water Change',
    icon: 'Droplets',
    logType: 'memo',
    color: '#16a34a',
    logs: [
      { id: '2-1', timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), note: '25% water change.' },
    ],
  },
  {
    id: '3',
    name: 'Workout',
    icon: 'Dumbbell',
    logType: 'counter',
    color: '#ef4444',
    logs: [
      { id: '3-1', timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000) },
      { id: '3-2', timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) },
    ],
  },
];

export const EventProvider = ({ children }: { children: ReactNode }) => {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    try {
      const storedEvents = localStorage.getItem('chronoLogEvents');
      if (storedEvents) {
        const parsedEvents = JSON.parse(storedEvents, (key, value) => {
          if (key === 'timestamp') {
            return new Date(value);
          }
          return value;
        });
        setEvents(parsedEvents);
      } else {
        setEvents(mockEvents);
      }
    } catch (error) {
      console.error("Failed to load events from localStorage", error);
      setEvents(mockEvents);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (!loading) {
      try {
        localStorage.setItem('chronoLogEvents', JSON.stringify(events));
      } catch (error) {
        console.error("Failed to save events to localStorage", error);
      }
    }
  }, [events, loading]);

  const addEvent = (name: string, icon: string, logType: LogType, color?: string) => {
    const newEvent: Event = {
      id: Date.now().toString(),
      name,
      icon,
      logType,
      color,
      logs: [],
    };
    setEvents(prevEvents => [...prevEvents, newEvent]);
  };

  const addLog = (eventId: string, logData: Partial<Omit<Log, 'id' | 'timestamp'>>, timestamp?: Date) => {
    setEvents(prevEvents =>
      prevEvents.map(event => {
        if (event.id === eventId) {
          const newLog: Log = {
            id: `${eventId}-${Date.now()}`,
            timestamp: timestamp || new Date(),
            ...logData,
          };
          const updatedLogs = [newLog, ...event.logs].sort((a,b) => b.timestamp.getTime() - a.timestamp.getTime());
          return { ...event, logs: updatedLogs };
        }
        return event;
      })
    );
  };

  const getEventById = (id: string) => {
    return events.find(event => event.id === id);
  };

  const updateEvent = (eventId: string, updatedData: Partial<Event>) => {
    setEvents(prevEvents =>
      prevEvents.map(event =>
        event.id === eventId ? { ...event, ...updatedData } : event
      )
    );
  };

  const deleteEvent = (eventId: string) => {
    setEvents(prevEvents => prevEvents.filter(event => event.id !== eventId));
  };
  
  const updateLog = (eventId: string, logId: string, updatedLogData: Partial<Omit<Log, 'id'>>) => {
    setEvents(prevEvents =>
      prevEvents.map(event => {
        if (event.id === eventId) {
          const updatedLogs = event.logs.map(log =>
            log.id === logId ? { ...log, ...updatedLogData } : log
          ).sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
          return { ...event, logs: updatedLogs };
        }
        return event;
      })
    );
  };

  const deleteLog = (eventId: string, logId: string) => {
    setEvents(prevEvents =>
      prevEvents.map(event => {
        if (event.id === eventId) {
          const updatedLogs = event.logs.filter(log => log.id !== logId);
          return { ...event, logs: updatedLogs };
        }
        return event;
      })
    );
  };
  
  const importEvents = (data: Event[]) => {
    // We need to re-instantiate date objects after JSON parsing
    const sanitizedData = data.map(event => ({
      ...event,
      logs: event.logs.map(log => ({
        ...log,
        timestamp: new Date(log.timestamp),
      })),
    }));
    setEvents(sanitizedData);
  };

  return (
    <EventContext.Provider value={{ events, loading, addEvent, addLog, getEventById, updateEvent, deleteEvent, updateLog, deleteLog, importEvents }}>
      {children}
    </EventContext.Provider>
  );
};
