
"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AddLogForm } from "./AddLogForm";
import { type Event } from "@/lib/types";

type AddLogDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  event: Event;
};

export function AddLogDialog({ open, onOpenChange, event }: AddLogDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add New Log</DialogTitle>
          <DialogDescription>Record a new entry for {event.name}.</DialogDescription>
        </DialogHeader>
        <AddLogForm
          event={event}
          onLogAdded={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
