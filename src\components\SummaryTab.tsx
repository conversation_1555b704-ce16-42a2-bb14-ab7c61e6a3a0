"use client";

import { useState } from "react";
import { type Event } from "@/lib/types";
import { analyzeLogs } from "@/ai/flows/analyze-logs-for-insights";
import { But<PERSON> } from "./ui/button";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "./ui/card";
import { <PERSON>bul<PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { Skeleton } from "./ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "./ui/alert";
import { Terminal } from "lucide-react";

type SummaryTabProps = {
  event: Event;
};

export function SummaryTab({ event }: SummaryTabProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [insights, setInsights] = useState<string | null>(null);
  const [suggestions, setSuggestions] = useState<string | null>(null);

  const handleAnalyze = async () => {
    setLoading(true);
    setError(null);
    setInsights(null);
    setSuggestions(null);
    try {
      if (event.logs.length < 3) {
        setError("Need at least 3 logs to provide meaningful insights.");
        setLoading(false);
        return;
      }
      const logData = JSON.stringify(
        event.logs.map(log => ({
          timestamp: log.timestamp,
          ...(log.amount !== undefined && { amount: log.amount }),
          ...(log.note && { note: log.note }),
        }))
      );

      const result = await analyzeLogs({ logData });
      setInsights(result.insights);
      setSuggestions(result.suggestions);
    } catch (e) {
      setError("An error occurred while analyzing the logs.");
      console.error(e);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-accent" />
            AI-Powered Log Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4 text-muted-foreground">
            Get intelligent insights and suggestions about your logging habits.
            Our AI will analyze your log data to find patterns and trends you might have missed.
          </p>
          <Button onClick={handleAnalyze} disabled={loading}>
            {loading ? "Analyzing..." : "Generate Insights"}
          </Button>
        </CardContent>
      </Card>
      
      {error && (
         <Alert variant="destructive">
            <Terminal className="h-4 w-4" />
            <AlertTitle>Analysis Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading && (
        <div className="grid gap-6 md:grid-cols-2">
            <Card>
                <CardHeader><Skeleton className="h-6 w-1/2" /></CardHeader>
                <CardContent className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                </CardContent>
            </Card>
            <Card>
                <CardHeader><Skeleton className="h-6 w-1/2" /></CardHeader>
                <CardContent className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                </CardContent>
            </Card>
        </div>
      )}

      {insights && suggestions && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card className="bg-card/80">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-primary" />
                Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">{insights}</p>
            </CardContent>
          </Card>
          <Card className="bg-card/80">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-primary" />
                Suggestions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">{suggestions}</p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
